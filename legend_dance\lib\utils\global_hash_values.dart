// 全局hashValues函数，用于解决第三方包兼容性问题
// 在项目入口导入此文件，为第三方包提供hashValues函数

/// 全局hashValues函数，兼容旧版本Flutter的hashValues API
/// 供第三方包使用，支持最多20个参数以兼容所有第三方库
int hashValues(
  Object? value1, [
  Object? value2,
  Object? value3,
  Object? value4,
  Object? value5,
  Object? value6,
  Object? value7,
  Object? value8,
  Object? value9,
  Object? value10,
  Object? value11,
  Object? value12,
  Object? value13,
  Object? value14,
  Object? value15,
  Object? value16,
  Object? value17,
  Object? value18,
  Object? value19,
  Object? value20,
]) {
  return Object.hash(
    value1, value2, value3, value4, value5,
    value6, value7, value8, value9, value10,
    value11, value12, value13, value14, value15,
    value16, value17, value18, value19, value20,
  );
}