// lib: , url: package:keepdance/pages/creation/services/dance_share_service.dart

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'dart:math';

import 'package:archive/archive_io.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:image/image.dart' as img;
import 'package:fluwx/fluwx.dart' as fluwx;
import 'package:keepdance/pages/creation/services/dance_encryption_service.dart'; // 推测路径
import 'package:keepdance/pages/creation/services/user_info_service.dart';       // 推测路径
import 'package:keepdance/pages/video_upload/services/local_storage_service.dart';
import 'package:logger/logger.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:share_plus/share_plus.dart';
import 'package:video_thumbnail/video_thumbnail.dart';

// DanceWorkModel、DanceMetadataModel、PackageInfoModel 等模型定义需要根据项目实际情况进行推测或引入。
// 这里为了代码能通过编译，会创建一些基础的模型类。
class DanceWorkModel {
  String? name;
  String? coverPath;
  String? videoPath;
  String? musicPath;
  String? bodyDataPath;
  // extras 可能是一个 Map
  Map<String, dynamic>? extras;

  DanceWorkModel({
    this.name,
    this.coverPath,
    this.videoPath,
    this.musicPath,
    this.bodyDataPath,
    this.extras,
  });
}

class UserShareInfo {
  String? sharerId;
  String? sharerName;
  String? sharerAvatar;
}

/// 舞蹈分享服务
abstract class DanceShareService {
  static late final Logger _logger = Logger();

  /// 清理文件名中的非法字符。
  static String _sanitizeFileName(String fileName) {
    // 替换所有非法字符为下划线
    String sanitized = fileName.replaceAll(RegExp(r'[<>:"/\\|?*]'), '_');
    // 替换一个或多个空白字符为下划线
    sanitized = sanitized.replaceAll(RegExp(r'\s+'), '_');
    // 去除首尾可能存在的下划线
    return sanitized.trim();
  }

  /// 分享舞蹈作品。
  /// [work] 是舞蹈作品模型。
  /// [shareType] 分享类型，例如 'wechat', 'system'。
  static Future<bool> shareDanceWork(DanceWorkModel work, String shareType) async {
    _logger.i("开始分享舞蹈作品: ${work.name}, 分享类型: $shareType");

    Directory? tempDir;
    try {
      tempDir = await _createTempDirectory();
      File? packageFile = await _packageDanceWork(work, tempDir);

      if (packageFile == null) {
        _logger.e("打包失败");
        await _cleanupTempFiles(tempDir);
        return false;
      }

      File optimizedFile = await _optimizeFileSize(packageFile, tempDir);

      bool success;
      if (shareType == 'wechat') {
        success = await _shareToWechat(optimizedFile, work);
      } else {
        success = await _shareToSystem(optimizedFile, work);
      }

      await _cleanupTempFiles(tempDir);

      // 这部分代码在原汇编中存在，但没有上下文来确定它们的具体用途。
      // 为保持功能完整性，予以保留，但可能需要根据实际业务逻辑调整。
      // StoreStaticField(0x14f8, r1)
      // StoreStaticField(0x14fc, r1)
      // StoreStaticField(0x1500, r1)

      if (success) {
        _logger.i("舞蹈作品分享成功: ${work.name}");
      } else {
        _logger.w("舞蹈作品分享失败: ${work.name}");
      }

      return success;
    } catch (e) {
      _logger.e("分享舞蹈作品失败: $e");
      if (tempDir != null) {
          await _cleanupTempFiles(tempDir);
      }
      return false;
    }
  }

  /// 通过系统分享。
  static Future<bool> _shareToSystem(File file, DanceWorkModel work) async {
    try {
      _logger.d("开始系统分享");
      int fileSize = await file.length();
      double fileSizeMB = fileSize / (1024 * 1024);
      _logger.i("准备系统分享文件: ${fileSizeMB.toStringAsFixed(1)}MB");

      if (fileSize > 1024 * 1024 * 1024) { // 1GB a
        _logger.w("系统分享被拒绝，文件大小超限: ${fileSizeMB.toStringAsFixed(1)}MB > 1GB");
        return false;
      }
      
      final xFile = XFile(file.path);
      List<XFile> filesToShare = [xFile];

      final result = await Share.shareXFiles(
        filesToShare,
        text: "${work.name} - 舞蹈作品分享\n来自热舞型动派对",
      );

      _logger.d("系统分享结果: ${result.status}");

      if (result.status == ShareResultStatus.success) {
        _logger.i("系统分享成功，文件大小: ${fileSizeMB.toStringAsFixed(1)}MB");
        return true;
      } else {
        _logger.w("系统分享失败，状态: ${result.status}");
        return false;
      }
    } catch (e) {
      _logger.e("系统分享失败: $e");
      return false;
    }
  }

  /// 分享到微信。
  static Future<bool> _shareToWechat(File file, DanceWorkModel work) async {
    try {
      _logger.d("开始微信文件分享");
      await _initWechatApi();

      bool isInstalled = await fluwx.isWeChatInstalled;
      if (!isInstalled) {
        _logger.w("微信未安装");
        return false;
      }

      int fileSize = await file.length();
      double fileSizeMB = fileSize / (1024 * 1024);

      _logger.i("准备微信分享文件，大小: ${fileSizeMB.toStringAsFixed(1)}MB");

      if (fileSize > 500 * 1024 * 1024) { // 微信文件分享大小限制约为500MB，这里保守一些
          _logger.w("微信分享被拒绝，文件大小超限: ${fileSizeMB.toStringAsFixed(1)}MB > 500MB");
          return false;
      }

      bool success = await _shareFileToWechat(file, work);
      if (success) {
        _logger.i("微信文件分享成功");
        return true;
      } else {
        _logger.w("微信文件分享失败，尝试系统分享");
        return await _shareToSystem(file, work);
      }
    } catch (e) {
        _logger.w("微信SDK分享异常，使用系统分享: $e");
        return await _shareToSystem(file, work);
    }
  }

  /// 调用Fluwx SDK分享文件到微信。
  static Future<bool> _shareFileToWechat(File file, DanceWorkModel work) async {
    late StreamSubscription<fluwx.WeChatShareResponse> subscriber;
    try {
      final fileModel = fluwx.WeChatFile.file(file);
      Uint8List? thumbnailData = await _generateThumbnail(work);

      if (thumbnailData == null || thumbnailData.isEmpty) {
        _logger.w("缩略图数据为空，微信分享可能无缩略图");
      } else {
        _logger.d("缩略图数据准备完成: ${thumbnailData.length} bytes");
      }

      fluwx.WeChatShareFileModel model = fluwx.WeChatShareFileModel(
        file.path,
        scene: fluwx.WeChatScene.session,
        title: work.name,
        description: "来自热舞型动派对的舞蹈作品分享",
      );

      _logger.d("微信分享模型创建完成 - 平台: Android, 标题: ${model.title}");

      final completer = Completer<bool>();
      subscriber = fluwx.responseEventHandler.listen((fluwx.WeChatResponse response) {
          _logger.d("微信文件分享结果: successful=${response.isSuccessful}, 错误码: ${response.errCode}, 错误信息: ${response.errStr}");
          if (response.errCode != 0) {
              _logger.w("微信分享失败详情 - 错误码: ${response.errCode}, 错误信息: ${response.errStr}");
          }
          if (!completer.isCompleted) {
            completer.complete(response.isSuccessful);
          }
      });
      
      await fluwx.share(model);

      bool result = await completer.future.timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          _logger.w("微信分享超时");
          return false;
        },
      );
      
      return result;
    } catch (e) {
      _logger.e("微信SDK文件分享失败: $e");
      return false;
    } finally {
      // 移除监听器
      await subscriber.cancel();
      _logger.d("微信分享监听器已移除");
    }
  }

  /// 生成缩略图。
  static Future<Uint8List?> _generateThumbnail(DanceWorkModel work) async {
    try {
        _logger.d("开始生成缩略图 for ${work.name}");
        if (work.coverPath != null && work.coverPath!.isNotEmpty) {
            _logger.d("尝试使用本地封面: ${work.coverPath}");
            File coverFile = File(work.coverPath!);
            if (await coverFile.exists()) {
                Uint8List coverData = await coverFile.readAsBytes();
                if (coverData.isNotEmpty && coverData.length < 32 * 1024 * 1024) { // 微信缩略图限制
                    _logger.i("直接使用原始封面文件作为缩略图，大小: ${coverData.length} bytes");
                    return coverData;
                } else {
                    _logger.w("封面文件为空或过大 (${coverData.length} bytes)，将尝试压缩处理...");
                    Uint8List? compressedData = await _compressImage(coverData);
                    if (compressedData != null && compressedData.isNotEmpty) {
                        _logger.i("使用压缩后的封面生成缩略图成功");
                        return compressedData;
                    }
                }
            } else {
                _logger.w("本地封面文件不存在: ${work.coverPath}");
            }
        }

        if (work.videoPath != null && work.videoPath!.isNotEmpty) {
            _logger.d("尝试从视频生成缩略图: ${work.videoPath}");
            File videoFile = File(work.videoPath!);
            if (await videoFile.exists()) {
                 _logger.d("视频文件存在，开始生成缩略图...");
                String? thumbnailPath = await VideoThumbnail.thumbnailFile(
                    video: work.videoPath!,
                    thumbnailPath: (await getTemporaryDirectory()).path,
                    imageFormat: ImageFormat.JPEG,
                    maxHeight: 120,
                    quality: 85,
                );

                if (thumbnailPath != null) {
                    Uint8List thumbnailData = await File(thumbnailPath).readAsBytes();
                    _logger.d("从视频生成缩略图成功: ${thumbnailData.length} bytes");
                    Uint8List? compressedData = await _compressImage(thumbnailData);
                    if (compressedData != null && compressedData.isNotEmpty) {
                        _logger.i("视频缩略图压缩成功");
                        return compressedData;
                    }
                } else {
                    _logger.w("视频缩略图生成失败 (thumbnailPath is null)");
                }
            } else {
                _logger.w("本地视频文件不存在: ${work.videoPath}");
            }
        }

        _logger.d("使用默认应用图标作为缩略图");
        Uint8List appIcon = await _getAppIcon();
        _logger.i("生成默认缩略图完成");
        return appIcon;

    } catch(e) {
        _logger.e("生成缩略图失败: $e");
        Uint8List fallbackIcon = await _getAppIcon();
        _logger.w("使用备用图标作为缩略图");
        return fallbackIcon;
    }
  }
  
  /// 获取应用图标作为备用缩略图。
  static Future<Uint8List> _getAppIcon() async {
    try {
        _logger.d("从assets加载应用图标: assets/images/ic_launcher.png");
        final byteData = await rootBundle.load('assets/images/ic_launcher.png');
        final buffer = byteData.buffer.asUint8List();
        _logger.i("成功加载应用图标: ${buffer.length} bytes");
        return buffer;
    } catch(e) {
        _logger.e("加载应用图标失败: $e. 返回一个最小的备用图片");
        return _createMinimalThumbnail();
    }
  }

  /// 创建一个最小的灰色JPG图像作为最终备用。
  static Uint8List _createMinimalThumbnail() {
    try {
      final image = img.Image(width: 120, height: 120);
      img.fill(image, color: img.ColorRgb8(200, 200, 200));
      return Uint8List.fromList(img.encodeJpg(image, quality: 80));
    } catch(e) {
      _logger.e("创建最小缩略图失败: $e");
      // 一个极小的、合法的JPG文件头，以防万一
      return Uint8List.fromList([
         0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
         0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
         0x00, 0x03, 0x02, 0x02, 0x02, 0x02, 0x02, 0x03, 0x02, 0x02, 0x02, 0x03,
         0x03, 0x03, 0x03, 0x04, 0x06, 0x04, 0x04, 0x04, 0x04, 0x04, 0x08, 0x06,
         0x06, 0x05, 0x06, 0x09, 0x08, 0x0A, 0x0A, 0x09, 0x08, 0x09, 0x09, 0x0A,
         0x0C, 0x0F, 0x0C, 0x0A, 0x0B, 0x0E, 0x0B, 0x09, 0x09, 0x0D, 0x11, 0x0D,
         0x0E, 0x0F, 0x10, 0x10, 0x11, 0x10, 0x0A, 0x0C, 0x12, 0x13, 0x12, 0x10,
         0x13, 0x0F, 0x10, 0x10, 0x10, 0xFF, 0xC9, 0x00, 0x0B, 0x08, 0x00, 0x01,
         0x00, 0x01, 0x01, 0x01, 0x11, 0x00, 0xFF, 0xCC, 0x00, 0x06, 0x00, 0x10,
         0x10, 0x05, 0xFF, 0xDA, 0x00, 0x08, 0x01, 0x01, 0x00, 0x00, 0x3F, 0x00,
         0xD2, 0xCF, 0x20, 0xFF, 0xD9
      ]);
    }
  }

  /// 压缩图片数据。
  static Future<Uint8List?> _compressImage(Uint8List imageData) async {
    try {
      img.Image? image = img.decodeImage(imageData);
      if (image == null) {
        _logger.w("图片解码失败");
        return null;
      }
      
      int width = image.width;
      int height = image.height;
      int newWidth, newHeight;
      if (width > height) {
        newWidth = min(width, 120);
        newHeight = (height * newWidth / width).round();
      } else {
        newHeight = min(height, 120);
        newWidth = (width * newHeight / height).round();
      }

      img.Image resizedImage = img.copyResize(image, width: newWidth, height: newHeight);
      Uint8List pngData = Uint8List.fromList(img.encodePng(resizedImage));

      _logger.d("缩略图以PNG格式压缩完成: ${pngData.length} bytes, 尺寸: ${resizedImage.width}x${resizedImage.height}");
      
      return pngData;
    } catch(e) {
      _logger.e("图片压缩失败: $e");
      return null;
    }
  }

  /// 初始化 Wechat API。
  static Future<void> _initWechatApi() async {
    try {
      await fluwx.registerApi(
        appId: "YOUR_WECHAT_APP_ID", // 需要替换为实际的AppID
        universalLink: "YOUR_UNIVERSAL_LINK" // 需要替换为实际的Universal Link
      );
      _logger.d("微信API初始化成功");
    } catch(e, s) {
      _logger.w("微信API初始化失败: $e");
      rethrow;
    }
  }

  /// 优化文件大小（当前实现为直接返回，可扩展压缩逻辑）。
  static Future<File> _optimizeFileSize(File inputFile, Directory tempDir) async {
      int fileSize = await inputFile.length();
      double fileSizeMB = fileSize / (1024 * 1024);
      _logger.d("原始包文件大小: ${fileSizeMB.toStringAsFixed(1)}MB ($fileSize bytes)");

      if (fileSize <= 1024*1024*1024) { // 1GB limit
          _logger.d("文件大小在1GB限制范围内，无需压缩");
          return inputFile;
      }

      _logger.i("文件大小超过1GB（当前${fileSizeMB.toStringAsFixed(1)}MB），开始压缩优化...");
      
      var archive = ZipDecoder().decodeBytes(await inputFile.readAsBytes());
      var newArchive = Archive();

      for (var file in archive) {
          if (file.name == 'video.mp4' && file.isFile) {
              Uint8List? compressedVideo = await _compressVideo(file.content as Uint8List);
              if (compressedVideo != null) {
                  _logger.i("视频压缩完成: ${file.size} -> ${compressedVideo.length} bytes");
                  newArchive.addFile(ArchiveFile(file.name, compressedVideo.length, compressedVideo));
              } else {
                  newArchive.addFile(file);
              }
          } else if(file.name == 'cover.jpg' && file.isFile) {
              Uint8List? compressedImage = await _compressImage(file.content as Uint8List);
              if(compressedImage != null){
                  _logger.i("封面压缩完成: ${file.size} -> ${compressedImage.length} bytes");
                  newArchive.addFile(ArchiveFile(file.name, compressedImage.length, compressedImage));
              } else {
                 newArchive.addFile(file);
              }
          } else {
              newArchive.addFile(file);
          }
      }

      var compressedBytes = ZipEncoder().encode(newArchive);
      if (compressedBytes == null) {
          _logger.e("文件压缩失败: ZipEncoder返回null");
          return inputFile; // 返回原始文件
      }

      String outputFileName = 'compressed_${path.basename(inputFile.path)}';
      File outputFile = File(path.join(tempDir.path, outputFileName));
      await outputFile.writeAsBytes(compressedBytes);

      double newFileSizeMB = outputFile.lengthSync() / (1024 * 1024);
      _logger.i("文件压缩完成: ${fileSizeMB.toStringAsFixed(1)}MB -> ${newFileSizeMB.toStringAsFixed(1)}MB");
      
      await inputFile.delete(); // 删除旧文件
      return outputFile;
  }

  /// 视频压缩功能(待实现)。
  static Future<Uint8List?> _compressVideo(Uint8List videoData) async {
    try {
      _logger.w("视频压缩功能待实现，当前返回原始视频数据（未压缩）");
      return Uint8List.fromList(videoData);
    } catch(e) {
      _logger.e("视频压缩失败: $e");
      return null;
    }
  }

  /// 清理临时文件
  static Future<void> _cleanupTempFiles(Directory tempDir) async {
    try {
      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
        _logger.d("临时文件清理完成");
      }
    } catch (e) {
      _logger.w("清理临时文件失败: $e");
    }
  }

  /// 打包舞蹈作品相关文件。
  static Future<File?> _packageDanceWork(DanceWorkModel work, Directory tempDir) async {
    _logger.d("开始打包作品数据: ${work.name}");
    try {
      var archive = Archive();

      // 添加视频文件
      if (work.videoPath != null && work.videoPath!.isNotEmpty) {
          var videoFile = File(work.videoPath!);
          if (await videoFile.exists()) {
              archive.addFile(ArchiveFile('video.mp4', await videoFile.length(), await videoFile.readAsBytes()));
              _logger.d("添加视频文件: ${await videoFile.length()} bytes");
          }
      }

      // 添加封面文件
      if (work.coverPath != null && work.coverPath!.isNotEmpty) {
        var coverFile = File(work.coverPath!);
        if (await coverFile.exists()){
            archive.addFile(ArchiveFile('cover.jpg', await coverFile.length(), await coverFile.readAsBytes()));
            _logger.d("添加封面图片: ${await coverFile.length()} bytes");
        }
      } else {
        await _generateThumbnailForArchive(work, archive);
      }

      // 添加加密的 BodyData
      if (work.bodyDataPath != null && work.bodyDataPath!.isNotEmpty) {
        var bodyDataFile = File(work.bodyDataPath!);
        if (await bodyDataFile.exists()) {
          String bodyDataContent = await bodyDataFile.readAsString();
          String? encryptedBodyData = await DanceEncryptionService.encryptBodyData(bodyDataContent);
          if (encryptedBodyData == null) {
            _logger.e("BodyData加密失败");
            return null;
          }
          final bodyDataBytes = utf8.encode(encryptedBodyData);
          archive.addFile(ArchiveFile('BodyData.txt', bodyDataBytes.length, bodyDataBytes));
          _logger.d("添加加密BodyData文件: ${bodyDataBytes.length} bytes");
        }
      }

      // 添加元数据
      Map<String, dynamic> metadata = await _createMetadata(work);
      String? encryptedMetadata = await DanceEncryptionService.encryptMetadata(json.encode(metadata));
      if (encryptedMetadata == null) {
         _logger.e("元数据加密失败");
         return null;
      }
      final metadataBytes = utf8.encode(encryptedMetadata);
      archive.addFile(ArchiveFile('metadata.json', metadataBytes.length, metadataBytes));
      _logger.d("添加加密元数据文件: ${metadataBytes.length} bytes");

      // 添加包信息
      Map<String, dynamic> packageInfo = await _createPackageInfo();
      final packageInfoBytes = utf8.encode(json.encode(packageInfo));
      archive.addFile(ArchiveFile('package_info.json', packageInfoBytes.length, packageInfoBytes));

      // 编码为 ZIP
      final zipData = ZipEncoder().encode(archive);
      if (zipData == null) {
        _logger.e("ZIP压缩失败");
        return null;
      }
      double zipSizeMB = zipData.length / (1024 * 1024);
      _logger.i("保存舞蹈包文件，ZIP大小: ${zipSizeMB.toStringAsFixed(2)} MB");

      // 保存到文件
      String fileName = '${_sanitizeFileName(work.name ?? 'untitled')}.kdance';
      File outputFile = File(path.join(tempDir.path, fileName));
      await outputFile.writeAsBytes(zipData);

      _logger.i("作品打包完成: ${outputFile.path}");
      return outputFile;

    } catch (e) {
      _logger.e("打包作品数据失败: $e");
      return null;
    }
  }

  /// 创建包信息。
  static Future<Map<String, dynamic>> _createPackageInfo() async {
    final packageInfo = await PackageInfo.fromPlatform();
    return {
      'packageVersion': '1.0.0', // 文件格式版本
      'appName': packageInfo.appName,
      'appVersion': packageInfo.version,
      'buildNumber': packageInfo.buildNumber,
      'platform': 'android', // 或者根据平台判断
      'createTime': DateTime.now().toIso8601String(),
      'fileFormat': 'kdance',
      'description': '热舞型动派对 - 舞蹈作品分享包',
    };
  }
  
  /// 创建元数据。
  static Future<Map<String, dynamic>> _createMetadata(DanceWorkModel work) async {
      int? actualVideoDuration = (work.extras?['actualVideoDuration'] as int?);
      int? metadataDuration = (work.extras?['metadataDuration'] as int?);
      int? duration = (work.extras?['duration'] as int?);

      int finalDuration = 0;
      if (actualVideoDuration != null && actualVideoDuration > 0) {
          finalDuration = actualVideoDuration;
          _logger.d("使用实际视频时长: $finalDuration秒");
      } else if (duration != null && duration > 0) {
          finalDuration = duration;
          _logger.d("使用extras中的时长: $finalDuration秒");
      } else if (metadataDuration != null && metadataDuration > 0) {
          finalDuration = metadataDuration;
          _logger.d("使用metadata中的时长: $finalDuration秒");
      } else {
        try {
          int? realTimeDuration = await _getRealTimeVideoDuration(work.videoPath!);
          if (realTimeDuration != null && realTimeDuration > 0) {
            finalDuration = realTimeDuration;
             _logger.i("通过实时分析获取视频时长: $finalDuration秒");
          } else {
             _logger.w("实时获取视频时长失败，保持默认值0秒");
          }
        } catch (e) {
             _logger.w("实时获取视频时长异常: $e，保持默认值0秒");
        }
      }
      
      _logger.i("分享时最终使用的时长: $finalDuration秒 (实际=$actualVideoDuration, extras=$duration, metadata=$metadataDuration)");
      
      String? sharerId;
      String? sharerName;
      String? sharerAvatar;

      try {
        if(Get.isRegistered<UserInfoService>()) {
          final Map<String, String?> info = Get.find<UserInfoService>().getUserShareInfo();
          sharerId = info['sharerId'];
          sharerName = info['sharerName'];
          sharerAvatar = info['sharerAvatar'];
          _logger.d("使用当前用户信息作为分享者: $sharerId - $sharerName");
        } else {
          _logger.w("UserInfoService未注册，无法获取当前用户信息");
        }
      } catch (e) {
          _logger.e("获取当前用户信息失败: $e");
      }

      if(work.extras?['sharerId'] != null && (work.extras!['sharerId'] as String).isNotEmpty){
          sharerId = work.extras!['sharerId'];
          sharerName = work.extras!['sharerName'];
          sharerAvatar = work.extras!['sharerAvatar'];
          _logger.d("使用作品原有分享者信息: $sharerId - $sharerName");
      }
      
      String finalDescription = work.extras?['description'] ?? '';
      if(finalDescription.isEmpty){
          try {
            if(Get.isRegistered<LocalStorageService>()){
              final danceVideo = await Get.find<LocalStorageService>().getDanceVideoById(work.extras?['id']);
              if(danceVideo?.description != null && danceVideo!.description!.isNotEmpty){
                  finalDescription = danceVideo.description!;
                  _logger.d("使用LocalDanceVideo中的描述: $finalDescription");
              }
            }
          } catch(e) {
             _logger.w("从LocalDanceVideo获取描述失败: $e");
          }
      }
      _logger.i("分享时最终使用的描述: $finalDescription");
      
      return {
          'id': work.extras?['id'],
          'title': work.name,
          'description': finalDescription,
          'createTime': DateTime.now().toIso8601String(),
          'fileSize': work.extras?['fileSize'] ?? 0,
          'duration': finalDuration,
          'category': {
              'id': work.extras?['categoryId'] ?? '1',
              'name': work.extras?['category'] ?? '流行舞',
          },
          'level': work.extras?['level'] ?? 'beginner',
          'intensity': work.extras?['intensity'] ?? 'moderate',
          'isMirrorEnabled': work.extras?['isMirrorEnabled'] ?? false,
          'showTips': work.extras?['showTips'] ?? true,
          'hasBodyData': work.extras?['hasBodyData'] ?? false,
          'scoreModel': work.extras?['scoreModel'] ?? '3',
          'videoResolution': {
              'width': work.extras?['videoWidth'],
              'height': work.extras?['videoHeight'],
              'isLandscape': work.extras?['isLandscape'],
              'aspectRatio': work.extras?['aspectRatio'],
          },
          'exportTime': DateTime.now().toIso8601String(),
          'exportVersion': (await PackageInfo.fromPlatform()).version,
          'sharerId': sharerId,
          'sharerName': sharerName,
          'sharerAvatar': sharerAvatar,
          'packageId': (await PackageInfo.fromPlatform()).packageName,
          'durationSources' : {
              'actual': actualVideoDuration,
              'extras': duration,
              'metadata': metadataDuration,
              'final': finalDuration
          }
      };
  }

  /// 获取实时视频时长（待实现）。
  static Future<int?> _getRealTimeVideoDuration(String videoPath) async {
    try {
      if (!(await File(videoPath).exists())){
        _logger.w("目标视频文件不存在: $videoPath");
        return null;
      }
      // TODO: 集成 video_player 或其他库来获取视频时长
      // 以下为占位代码
      _logger.d("实时视频时长获取功能待实现（需要集成video_player包）");
      return null; 
    } catch(e) {
      _logger.e("实时获取视频时长失败: $e");
      return null;
    }
  }

  /// 为归档文件生成缩略图。
  static Future<void> _generateThumbnailForArchive(DanceWorkModel work, Archive archive) async {
    try {
        final videoPath = work.videoPath;
        if(videoPath == null || videoPath.isEmpty) return;

        final thumbnailPath = await VideoThumbnail.thumbnailFile(
            video: videoPath,
            thumbnailPath: (await getTemporaryDirectory()).path,
            maxHeight: 400,
            quality: 75
        );
        if(thumbnailPath != null) {
            final thumbnailFile = File(thumbnailPath);
            if(await thumbnailFile.exists()){
                final thumbnailData = await thumbnailFile.readAsBytes();
                archive.addFile(ArchiveFile('cover.jpg', thumbnailData.length, thumbnailData));
                _logger.d("生成并添加视频缩略图: ${thumbnailData.length} bytes");
                await thumbnailFile.delete();
            }
        }
    } catch(e) {
        _logger.w("生成视频缩略图失败: $e");
    }
  }

  /// 创建临时目录。
  static Future<Directory> _createTempDirectory() async {
    final tempPath = (await getTemporaryDirectory()).path;
    final dirName = 'dance_share_${DateTime.now().millisecondsSinceEpoch}';
    final tempDir = Directory(path.join(tempPath, dirName));
    return await tempDir.create(recursive: true);
  }
}

