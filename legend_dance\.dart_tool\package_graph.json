{"roots": ["keepdance"], "packages": [{"name": "keepdance", "version": "1.0.0+1", "dependencies": ["async", "better_player", "cached_network_image", "camera", "connectivity_plus", "crypto", "cupertino_icons", "device_info_plus", "encrypt", "flutter", "flutter_screenutil", "fluwx", "get", "get_storage", "http", "hugeicons", "image", "json_annotation", "json_serializable", "logger", "l<PERSON>yin", "package_info_plus", "palette_generator", "path", "path_provider", "permission_handler", "pull_to_refresh", "share_plus", "shared_preferences", "video_player", "video_thumbnail"], "devDependencies": ["build_runner", "flutter_lints", "flutter_test"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "build_runner", "version": "2.8.0", "dependencies": ["analyzer", "args", "async", "build", "build_config", "build_daemon", "built_collection", "built_value", "code_builder", "collection", "convert", "crypto", "dart_style", "frontend_server_client", "glob", "graphs", "http_multi_server", "io", "json_annotation", "logging", "meta", "mime", "package_config", "path", "pool", "pub_semver", "shelf", "shelf_web_socket", "stack_trace", "stream_transform", "watcher", "web_socket_channel", "yaml"]}, {"name": "flutter_lints", "version": "5.0.0", "dependencies": ["lints"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "video_thumbnail", "version": "0.5.6", "dependencies": ["flutter"]}, {"name": "share_plus", "version": "11.1.0", "dependencies": ["cross_file", "ffi", "file", "flutter", "flutter_web_plugins", "meta", "mime", "share_plus_platform_interface", "url_launcher_linux", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows", "web", "win32"]}, {"name": "fluwx", "version": "5.7.2", "dependencies": ["flutter", "flutter_web_plugins", "plugin_platform_interface"]}, {"name": "camera", "version": "0.11.0", "dependencies": ["camera_android_camerax", "camera_avfoundation", "camera_platform_interface", "camera_web", "flutter", "flutter_plugin_android_lifecycle"]}, {"name": "palette_generator", "version": "0.3.3", "dependencies": ["collection", "flutter", "path"]}, {"name": "better_player", "version": "0.0.84", "dependencies": ["collection", "cupertino_icons", "flutter", "flutter_widget_from_html_core", "meta", "path_provider", "visibility_detector", "wakelock_plus", "xml"]}, {"name": "get_storage", "version": "2.1.1", "dependencies": ["flutter", "get", "path_provider"]}, {"name": "l<PERSON>yin", "version": "2.0.3", "dependencies": []}, {"name": "encrypt", "version": "5.0.3", "dependencies": ["args", "asn1lib", "clock", "collection", "crypto", "pointycastle"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "hugeicons", "version": "0.0.11", "dependencies": ["flutter"]}, {"name": "pull_to_refresh", "version": "2.0.0", "dependencies": ["flutter"]}, {"name": "connectivity_plus", "version": "6.1.5", "dependencies": ["collection", "connectivity_plus_platform_interface", "flutter", "flutter_web_plugins", "meta", "nm", "web"]}, {"name": "package_info_plus", "version": "8.3.1", "dependencies": ["clock", "ffi", "flutter", "flutter_web_plugins", "http", "meta", "package_info_plus_platform_interface", "path", "web", "win32"]}, {"name": "json_serializable", "version": "6.11.1", "dependencies": ["analyzer", "async", "build", "build_config", "dart_style", "json_annotation", "meta", "path", "pub_semver", "pubspec_parse", "source_gen", "source_helper"]}, {"name": "image", "version": "4.5.4", "dependencies": ["archive", "meta", "xml"]}, {"name": "video_player", "version": "2.10.0", "dependencies": ["flutter", "html", "video_player_android", "video_player_avfoundation", "video_player_platform_interface", "video_player_web"]}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "device_info_plus", "version": "9.1.2", "dependencies": ["device_info_plus_platform_interface", "ffi", "file", "flutter", "flutter_web_plugins", "meta", "win32", "win32_registry"]}, {"name": "permission_handler", "version": "11.4.0", "dependencies": ["flutter", "meta", "permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_platform_interface", "permission_handler_windows"]}, {"name": "cached_network_image", "version": "3.4.1", "dependencies": ["cached_network_image_platform_interface", "cached_network_image_web", "flutter", "flutter_cache_manager", "octo_image"]}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "http", "version": "1.5.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "shared_preferences", "version": "2.5.3", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "flutter_screenutil", "version": "5.9.3", "dependencies": ["flutter"]}, {"name": "logger", "version": "2.6.1", "dependencies": ["meta"]}, {"name": "get", "version": "4.7.2", "dependencies": ["flutter", "web"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "web_socket_channel", "version": "3.0.3", "dependencies": ["async", "crypto", "stream_channel", "web", "web_socket"]}, {"name": "watcher", "version": "1.1.3", "dependencies": ["async", "path"]}, {"name": "stream_transform", "version": "2.1.1", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "shelf_web_socket", "version": "3.0.0", "dependencies": ["shelf", "stream_channel", "web_socket_channel"]}, {"name": "shelf", "version": "1.4.2", "dependencies": ["async", "collection", "http_parser", "path", "stack_trace", "stream_channel"]}, {"name": "pub_semver", "version": "2.2.0", "dependencies": ["collection"]}, {"name": "pool", "version": "1.5.1", "dependencies": ["async", "stack_trace"]}, {"name": "package_config", "version": "2.2.0", "dependencies": ["path"]}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "io", "version": "1.0.5", "dependencies": ["meta", "path", "string_scanner"]}, {"name": "http_multi_server", "version": "3.2.2", "dependencies": ["async"]}, {"name": "graphs", "version": "2.3.2", "dependencies": ["collection"]}, {"name": "glob", "version": "2.1.3", "dependencies": ["async", "collection", "file", "path", "string_scanner"]}, {"name": "frontend_server_client", "version": "4.0.0", "dependencies": ["async", "path"]}, {"name": "dart_style", "version": "3.1.2", "dependencies": ["analyzer", "args", "collection", "package_config", "path", "pub_semver", "source_span", "yaml"]}, {"name": "convert", "version": "3.1.2", "dependencies": ["typed_data"]}, {"name": "code_builder", "version": "4.10.1", "dependencies": ["built_collection", "built_value", "collection", "matcher", "meta"]}, {"name": "built_value", "version": "8.12.0", "dependencies": ["built_collection", "collection", "fixnum", "meta"]}, {"name": "built_collection", "version": "5.1.1", "dependencies": []}, {"name": "build_daemon", "version": "4.0.4", "dependencies": ["built_collection", "built_value", "crypto", "http_multi_server", "logging", "path", "pool", "shelf", "shelf_web_socket", "stream_transform", "watcher", "web_socket_channel"]}, {"name": "build_config", "version": "1.2.0", "dependencies": ["checked_yaml", "json_annotation", "path", "pubspec_parse"]}, {"name": "build", "version": "4.0.0", "dependencies": ["analyzer", "crypto", "glob", "logging", "package_config", "path"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "analyzer", "version": "8.1.1", "dependencies": ["_fe_analyzer_shared", "collection", "convert", "crypto", "glob", "meta", "package_config", "path", "pub_semver", "source_span", "watcher", "yaml"]}, {"name": "lints", "version": "5.1.1", "dependencies": []}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "win32", "version": "5.14.0", "dependencies": ["ffi"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "url_launcher_platform_interface", "version": "2.3.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "url_launcher_linux", "version": "3.2.1", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_windows", "version": "3.1.4", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_web", "version": "2.4.1", "dependencies": ["flutter", "flutter_web_plugins", "url_launcher_platform_interface", "web"]}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "share_plus_platform_interface", "version": "6.1.0", "dependencies": ["cross_file", "flutter", "meta", "mime", "path_provider", "plugin_platform_interface", "uuid"]}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "cross_file", "version": "0.3.4+2", "dependencies": ["meta", "web"]}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "flutter_plugin_android_lifecycle", "version": "2.0.30", "dependencies": ["flutter"]}, {"name": "camera_web", "version": "0.3.5", "dependencies": ["camera_platform_interface", "flutter", "flutter_web_plugins", "stream_transform", "web"]}, {"name": "camera_platform_interface", "version": "2.10.0", "dependencies": ["cross_file", "flutter", "plugin_platform_interface", "stream_transform"]}, {"name": "camera_avfoundation", "version": "0.9.21+2", "dependencies": ["camera_platform_interface", "flutter", "stream_transform"]}, {"name": "camera_android_camerax", "version": "0.6.19+1", "dependencies": ["async", "camera_platform_interface", "flutter", "meta", "stream_transform"]}, {"name": "xml", "version": "6.6.1", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "visibility_detector", "version": "0.4.0+2", "dependencies": ["flutter"]}, {"name": "flutter_widget_from_html_core", "version": "0.15.2", "dependencies": ["csslib", "flutter", "html", "logging"]}, {"name": "wakelock_plus", "version": "1.3.2", "dependencies": ["dbus", "flutter", "flutter_web_plugins", "meta", "package_info_plus", "wakelock_plus_platform_interface", "web", "win32"]}, {"name": "pointycastle", "version": "3.9.1", "dependencies": ["collection", "convert", "js"]}, {"name": "asn1lib", "version": "1.6.5", "dependencies": []}, {"name": "nm", "version": "0.5.0", "dependencies": ["dbus"]}, {"name": "connectivity_plus_platform_interface", "version": "2.0.1", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "package_info_plus_platform_interface", "version": "3.2.1", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "source_helper", "version": "1.3.8", "dependencies": ["analyzer", "source_gen"]}, {"name": "source_gen", "version": "4.0.1", "dependencies": ["analyzer", "async", "build", "dart_style", "glob", "path", "pub_semver", "source_span", "yaml"]}, {"name": "pubspec_parse", "version": "1.5.0", "dependencies": ["checked_yaml", "collection", "json_annotation", "pub_semver", "yaml"]}, {"name": "archive", "version": "4.0.7", "dependencies": ["crypto", "path", "posix"]}, {"name": "video_player_web", "version": "2.4.0", "dependencies": ["flutter", "flutter_web_plugins", "video_player_platform_interface", "web"]}, {"name": "video_player_platform_interface", "version": "6.4.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "video_player_avfoundation", "version": "2.8.4", "dependencies": ["flutter", "video_player_platform_interface"]}, {"name": "video_player_android", "version": "2.8.13", "dependencies": ["flutter", "video_player_platform_interface"]}, {"name": "html", "version": "0.15.6", "dependencies": ["csslib", "source_span"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_foundation", "version": "2.4.2", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "path_provider_android", "version": "2.2.18", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "win32_registry", "version": "1.1.5", "dependencies": ["ffi", "win32"]}, {"name": "device_info_plus_platform_interface", "version": "7.0.3", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "permission_handler_platform_interface", "version": "4.3.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "permission_handler_windows", "version": "0.2.1", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_html", "version": "0.1.3+5", "dependencies": ["flutter", "flutter_web_plugins", "permission_handler_platform_interface", "web"]}, {"name": "permission_handler_apple", "version": "9.4.7", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_android", "version": "12.1.0", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "octo_image", "version": "2.1.0", "dependencies": ["flutter"]}, {"name": "flutter_cache_manager", "version": "3.4.1", "dependencies": ["clock", "collection", "file", "flutter", "http", "path", "path_provider", "rxdart", "sqflite", "uuid"]}, {"name": "cached_network_image_web", "version": "1.3.1", "dependencies": ["cached_network_image_platform_interface", "flutter", "flutter_cache_manager", "web"]}, {"name": "cached_network_image_platform_interface", "version": "4.1.1", "dependencies": ["flutter", "flutter_cache_manager"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "shared_preferences_windows", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.4.3", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_foundation", "version": "2.5.4", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_android", "version": "2.4.12", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "web_socket", "version": "1.0.1", "dependencies": ["web"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "checked_yaml", "version": "2.0.4", "dependencies": ["json_annotation", "source_span", "yaml"]}, {"name": "_fe_analyzer_shared", "version": "88.0.0", "dependencies": ["meta"]}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "petitparser", "version": "7.0.1", "dependencies": ["collection", "meta"]}, {"name": "csslib", "version": "1.0.2", "dependencies": ["source_span"]}, {"name": "dbus", "version": "0.7.11", "dependencies": ["args", "ffi", "meta", "xml"]}, {"name": "wakelock_plus_platform_interface", "version": "1.2.3", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "js", "version": "0.7.2", "dependencies": []}, {"name": "posix", "version": "6.0.3", "dependencies": ["ffi", "meta", "path"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "sqflite", "version": "2.4.2", "dependencies": ["flutter", "path", "sqflite_android", "sqflite_common", "sqflite_darwin", "sqflite_platform_interface"]}, {"name": "rxdart", "version": "0.28.0", "dependencies": []}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}, {"name": "sqflite_common", "version": "2.5.6", "dependencies": ["meta", "path", "synchronized"]}, {"name": "sqflite_platform_interface", "version": "2.4.0", "dependencies": ["flutter", "meta", "platform", "plugin_platform_interface", "sqflite_common"]}, {"name": "sqflite_darwin", "version": "2.4.2", "dependencies": ["flutter", "meta", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "sqflite_android", "version": "2.4.1", "dependencies": ["flutter", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "synchronized", "version": "3.4.0", "dependencies": []}], "configVersion": 1}