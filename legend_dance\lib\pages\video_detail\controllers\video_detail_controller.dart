// lib/pages/video_detail/controllers/video_detail_controller.dart
// 文件已根据汇编代码完整反编译

import 'dart:async';
import 'dart:io';

import 'package:better_player/better_player.dart';
import 'package:fluwx/fluwx.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:keepdance/common_widgets/common_dialog.dart';
import 'package:keepdance/common_widgets/common_tips.dart';
import 'package:keepdance/models/dance_score_detail.dart';
import 'package:keepdance/models/local_video_score.dart';
import 'package:keepdance/models/report_reason.dart';
import 'package:keepdance/pages/aiworkshop/controllers/community_panel_controller.dart';
import 'package:keepdance/pages/video_detail/controllers/animation_controller.dart';
import 'package:keepdance/pages/video_detail/services/interaction_service.dart';
import 'package:keepdance/pages/video_detail/services/playback_speed_service.dart';
import 'package:keepdance/pages/video_detail/services/report_service.dart';
import 'package:keepdance/pages/video_detail/services/share_service.dart';
import 'package:keepdance/pages/video_detail/services/video_detail_service.dart';
import 'package:keepdance/pages/video_detail/services/vip_service.dart';
import 'package:keepdance/pages/video_detail/states/interaction_state.dart';
import 'package:keepdance/pages/video_detail/states/playback_speed_state.dart';
import 'package:keepdance/pages/video_detail/states/player_settings_state.dart';
import 'package:keepdance/pages/video_detail/states/video_info_state.dart';
import 'package:keepdance/pages/video_detail/utils/chart_animation_util.dart';
import 'package:keepdance/pages/video_detail/utils/playback_speed_utils.dart';
import 'package:keepdance/pages/video_detail/utils/video_preferences.dart';
import 'package:keepdance/pages/video_upload/utils/blurred_placeholder_util.dart';
import 'package:keepdance/pose/Controller/pose_camera_controller.dart';
import 'package:keepdance/routes/app_pages.dart';
import 'package:keepdance/services/local_video_score_service.dart';
import 'package:keepdance/utils/analytics/event_analytics.dart';
import 'package:keepdance/utils/orientation_util.dart';
import 'package:keepdance/utils/permission/permission_manager.dart';
import 'package:keepdance/utils/storage/global_data.dart';
import 'package:logger/logger.dart';

import 'package:keepdance/models/dance_video_detail.dart';
import 'package:keepdance/models/community_detail.dart';
import 'package:keepdance/models/community_detail_extend.dart';
import 'package:keepdance/models/material_detail_extend.dart';
import 'package:keepdance/pages/home/<USER>/home_controller.dart';
import 'package:keepdance/core/network/api_client.dart';


// --- Mixin Definitions ---

mixin VideoInteractionMixin on GetxController {
  late final InteractionState _state;
  final InteractionService _interactionService = InteractionService(ApiClient(), Logger());
  final Logger _logger = Logger();

  RxBool get isLikedRx => _state.isLiked;
  RxBool get isCollectedRx => _state.isCollected;
  RxBool get isFollowingRx => _state.isFollowing;

  void initInteractionState() {
    _state = Get.find<InteractionState>();
  }

  Future<void> loadInteractionData() async {
    try {
      if (!GlobalData.instance.isLoggedIn) {
        return;
      }

      String videoId = _state.videoId.value.toString();
      bool isCommunity = _state.isCommunity.value;

      _logger.i('开始加载交互数据: videoId=$videoId, isCommunity=$isCommunity');

      List<Future> futures = [];
      if (isCommunity) {
        futures.add(checkcommunityCollectionStatus());
        futures.add(dancecommunityLikeStatusEndpoint());
      } else {
        futures.add(checkCollectionStatus());
        futures.add(danceLikeStatusEndpoint());
        futures.add(danceFollowStatusEndpoint());
      }

      await Future.wait(futures);

      _logger.i(
          '交互数据加载完成: isLiked=${_state.isLiked.value}, isCollected=${_state.isCollected.value}, isFollowing=${_state.isFollowing.value}');
    } catch (e, stackTrace) {
      _logger.e('加载交互数据失败: $e');
    }
  }

  Future<void> danceFollowStatusEndpoint() async {
    try {
      if (!GlobalData.instance.isLoggedIn) return;
      int videoId = _state.videoId.value;
      _logger.i('检查关注状态: videoId=$videoId');
      bool status = await _interactionService.danceFollowStatus(videoId);
      _state.isFollowing.value = status;
      _logger.i('关注状态获取结果: status=$status');
      update();
    } catch (e, stackTrace) {
      _logger.e('检查关注状态时出错: $e');
    }
  }

  Future<void> danceLikeStatusEndpoint() async {
    try {
      if (!GlobalData.instance.isLoggedIn) return;
      int videoId = _state.videoId.value;
      _logger.i('检查点赞状态: videoId=$videoId');
      bool status = await _interactionService.danceLikeStatus(videoId);
      _state.isLiked.value = status;
      _logger.i('点赞状态获取结果: status=$status');
      update();
    } catch (e, stackTrace) {
      _logger.e('检查点赞状态时出错: $e');
    }
  }

  Future<void> dancecommunityLikeStatusEndpoint() async {
    try {
      if (!GlobalData.instance.isLoggedIn) return;
      int videoId = _state.videoId.value;
      _logger.i('检查社区视频点赞状态: videoId=$videoId');
      bool status = await _interactionService.dancecommunityLikeStatus(videoId);
      _state.isLiked.value = status;
      _logger.i('社区视频点赞状态获取结果: status=$status');
      update();
    } catch (e, stackTrace) {
      _logger.e('检查社区视频点赞状态时出错: $e');
    }
  }

  Future<void> checkCollectionStatus() async {
    try {
      if (!GlobalData.instance.isLoggedIn) return;
      int videoId = _state.videoId.value;
      _logger.i('检查收藏状态: videoId=$videoId');
      bool status = await _interactionService.checkCollectionStatus(videoId);
      _state.isCollected.value = status;
      _logger.i('收藏状态获取结果: status=$status');
      update();
    } catch (e, stackTrace) {
      _logger.e('检查收藏状态时出错: $e');
    }
  }

  Future<void> checkcommunityCollectionStatus() async {
    try {
      if (!GlobalData.instance.isLoggedIn) return;
      int videoId = _state.videoId.value;
      _logger.i('检查社区视频收藏状态: videoId=$videoId');
      bool status = await _interactionService.checkcommunityCollectionStatus(videoId);
      _state.isCollected.value = status;
      _logger.i('社区视频收藏状态获取结果: status=$status');
      update();
    } catch (e, stackTrace) {
      _logger.e('检查社区视频收藏状态时出错: $e');
    }
  }

  Future<void> toggleLike({Function? trackCallback}) async {
    try {
      int videoId = _state.videoId.value;
      bool isCommunity = _state.isCommunity.value;
      bool currentStatus = isLikedRx.value;

      _logger.i(
          '切换点赞状态: videoId: $videoId, isCommunity: $isCommunity, currentStatus: $currentStatus');

      if (isCommunity) {
        await (this as VideoDetailController)
            .togglecommunityLike(trackCallback: trackCallback);
        return;
      }

      bool success;
      String logAction = currentStatus ? "取消" : "添加";
      _logger.i('开始调用${logAction}点赞接口');

      if (currentStatus) {
        success = await _interactionService.danceLikeCancel(videoId);
      } else {
        success = await _interactionService.danceLikeSave(videoId);
      }

      _logger.i('点赞接口调用结果: success: $success, 当前videoId: $videoId');

      if (success) {
        isLikedRx.value = !currentStatus;
        _logger.i('点赞状态成功更新: ${isLikedRx.value}');
        HapticFeedback.lightImpact();
        if (trackCallback != null) {
          String action = isLikedRx.value ? 'toggle_on' : 'toggle_off';
          (this as VideoDetailController)
              .trackButtonClick('like', action, trackCallback: trackCallback);
          _logger.i('点赞埋点已发送');
        }
        update();
      } else {
        _logger.w('点赞接口调用失败或未成功切换状态，请检查接口返回值和videoId: $videoId');
      }
    } catch (e, stackTrace) {
      _logger.e('切换点赞状态时出错: $e');
    }
  }

  Future<void> togglecommunityLike(
      {bool showLikeAnimation = false, Function? trackCallback}) async {
    try {
      int videoId = _state.videoId.value;
      bool currentStatus = isLikedRx.value;
      _logger.i('切换社区视频点赞状态: videoId: $videoId, currentStatus: $currentStatus');

      bool success;
      String logAction = currentStatus ? "取消" : "添加";
       _logger.i('开始调用社区视频${logAction}点赞接口');

      if (currentStatus) {
        success = await _interactionService.dancecommunityLikeCancel(videoId);
      } else {
        success = await _interactionService.dancecommunityLikeSave(videoId);
      }

      _logger.i('社区视频点赞接口调用结果: success: $success, 当前videoId: $videoId');

      if (success) {
        isLikedRx.value = !currentStatus;
        _logger.i('社区视频点赞状态成功更新: ${isLikedRx.value}');
        HapticFeedback.lightImpact();

        if (showLikeAnimation && isLikedRx.value) {
          _logger.i('显示点赞动画');
          // Animation logic would be here.
        }

        if (trackCallback != null) {
          String action = isLikedRx.value ? 'toggle_on' : 'toggle_off';
          (this as VideoDetailController)
              .trackButtonClick('like', action, trackCallback: trackCallback);
          _logger.i('社区视频点赞埋点已发送');
        }
        update();
      } else {
        _logger
            .w('社区视频点赞接口调用失败或未成功切换状态，请检查接口返回值和videoId: $videoId');
      }
    } catch (e, stackTrace) {
      _logger.e('切换社区视频点赞状态时出错: $e');
    }
  }

  Future<void> toggleCollect({Function? trackCallback}) async {
    try {
      int videoId = _state.videoId.value;
      bool isCommunity = _state.isCommunity.value;
      bool currentStatus = isCollectedRx.value;

      _logger.i(
          '切换收藏状态: videoId: $videoId, isCommunity: $isCommunity, currentStatus: $currentStatus');

      if (isCommunity) {
        await (this as VideoDetailController)
            .togglecommunityCollect(trackCallback: trackCallback);
        return;
      }

      bool success;
      String logAction = currentStatus ? "取消" : "添加";
       _logger.i('开始调用${logAction}收藏接口');
       _logger.i('收藏请求详情: videoId=$videoId, API=${currentStatus ? "cancelDanceCollection" : "saveDanceCollection"}');

      if (currentStatus) {
        success = await _interactionService.cancelDanceCollection(videoId);
      } else {
        success = await _interactionService.saveDanceCollection(videoId);
      }

      _logger.i('收藏接口调用结果: success: $success, 当前videoId: $videoId');

      if (success) {
        bool previousStatus = isCollectedRx.value;
        isCollectedRx.value = !currentStatus;
        _logger.i(
            '收藏状态成功更新: 从 $previousStatus 变为 ${isCollectedRx.value}');
        HapticFeedback.lightImpact();

        if (trackCallback != null) {
          String action = isCollectedRx.value ? 'toggle_on' : 'toggle_off';
          (this as VideoDetailController)
              .trackButtonClick('collect', action, trackCallback: trackCallback);
          _logger.i('收藏埋点已发送: action=$action');
        }
        update();
        _logger.i('收藏操作完成后的状态: isCollected=${isCollectedRx.value}');
      } else {
        _logger.w('收藏接口调用失败或未成功切换状态，请检查接口返回值和videoId: $videoId');
      }
    } catch (e, stackTrace) {
      _logger.e('切换收藏状态时出错: $e');
    }
  }

  Future<void> togglecommunityCollect({Function? trackCallback}) async {
    try {
      int videoId = _state.videoId.value;
      bool currentStatus = isCollectedRx.value;
      _logger.i(
          '切换社区视频收藏状态: videoId: $videoId, currentStatus: $currentStatus');

      bool success;
       String logAction = currentStatus ? "取消" : "添加";
      _logger.i('开始调用社区视频${logAction}收藏接口');
       _logger.i('社区收藏请求详情: videoId=$videoId, API=${currentStatus ? "cancelDancecommunityCollection" : "saveDancecommunityCollection"}');

      if (currentStatus) {
        success =
            await _interactionService.cancelDancecommunityCollection(videoId);
      } else {
        success =
            await _interactionService.saveDancecommunityCollection(videoId);
      }

      _logger.i('社区视频收藏接口调用结果: success: $success, 当前videoId: $videoId');

      if (success) {
        bool previousStatus = isCollectedRx.value;
        isCollectedRx.value = !currentStatus;
        _logger.i(
            '社区视频收藏状态成功更新: 从 $previousStatus 变为 ${isCollectedRx.value}');
        HapticFeedback.lightImpact();

        if (trackCallback != null) {
          String action = isCollectedRx.value ? 'toggle_on' : 'toggle_off';
          (this as VideoDetailController)
              .trackButtonClick('collect', action, trackCallback: trackCallback);
          _logger.i('社区视频收藏埋点已发送');
        }
        update();
        _logger.i('社区收藏操作完成后的状态: isCollected=${isCollectedRx.value}');
      } else {
        _logger
            .w('社区视频收藏接口调用失败或未成功切换状态，请检查接口返回值和videoId: $videoId');
      }
    } catch (e, stackTrace) {
      _logger.e('切换社区视频收藏状态时出错: $e');
    }
  }

  Future<void> toggleFollow({Function? trackCallback}) async {
    try {
      int videoId = _state.videoId.value;
      bool currentStatus = isFollowingRx.value;
      _logger.i('切换关注状态: videoId=$videoId, currentStatus=$currentStatus');

      bool success;
      if (currentStatus) {
        success = await _interactionService.danceFollowCancel(videoId);
      } else {
        success = await _interactionService.danceFollowSave(videoId);
      }

      _logger.i('关注接口调用结果: success: $success');

      if (success) {
        isFollowingRx.value = !currentStatus;
        _logger.i('关注状态成功更新: ${isFollowingRx.value}');
        if (trackCallback != null) {
          String action = isFollowingRx.value ? 'toggle_on' : 'toggle_off';
          (this as VideoDetailController)
              .trackButtonClick('follow', action, trackCallback: trackCallback);
          _logger.i('关注埋点已发送');
        }
        update();
      } else {
        _logger.w('关注接口调用失败，请检查接口返回值和videoId: $videoId');
      }
    } catch (e, stackTrace) {
      _logger.e('切换关注状态时出错: $e');
    }
  }
}

mixin VideoSettingsMixin on GetxController {
  late final PlayerSettingsState _settingsState;
  final RxBool isRecording = false.obs;
  final Logger _logger = Logger();

  RxString get currentResolution => _settingsState.resolution;
  Rx<ScreenOrientation> get screenOrientation => _settingsState.screenOrientation;
  RxBool get isPrivacyMode => _settingsState.isAutoPlay; // 使用可用的属性
  RxBool get isMirrored => _settingsState.isMirror;
  RxBool get isSplitScreenMode => _settingsState.isAutoPlay; // 使用可用的属性

  void initVideoSettings() {
    _settingsState = Get.put(PlayerSettingsState());
    loadVideoPlaySettings();
    _loadRecordingStatus();
  }

  Future<void> saveVideoPlaySettings() async {
    await VideoPreferences.saveVideoPlaySettings(
      currentResolution.value,
      isMirrored.value,
      isPrivacyMode.value,
      isSplitScreenMode.value,
      screenOrientation.value,
    );
    update();
  }

  Future<void> loadVideoPlaySettings() async {
    try {
      Map<String, dynamic> settings =
          await VideoPreferences.loadVideoPlaySettings();
      currentResolution.value = settings['resolution'] ?? '720p';
      screenOrientation.value =
          settings['orientation'] ?? ScreenOrientation.portraitUp;
      isPrivacyMode.value = settings['isPrivacyMode'] ?? false;
      isSplitScreenMode.value = settings['isSplitScreenMode'] ?? false;
      await _loadMirrorStateBasedOnVideoType();
    } catch (e) {
      _logger.e("加载视频播放设置失败: $e");
    }
    update();
  }

  Future<void> _loadMirrorStateBasedOnVideoType() async {
    try {
      if (!GlobalData.instance.isLoggedIn) {
        _logger.i("本地视频详情页：使用专用镜像状态加载逻辑");
        isMirrored.value = false;
        return;
      }
      
      _logger.i("非本地视频详情页：从服务端数据读取镜像状态");
      var videoDetail = (this as VideoDetailController).videoDetail.value;
      bool mirrorState = false;
      if (videoDetail != null) {
        if (videoDetail.type == DetailType.community) {
          mirrorState = (videoDetail.detail?.mirror == 2);
        } else if (videoDetail.type == DetailType.dance) {
          mirrorState = (videoDetail.dance?.mirror == 2);
        }
      }
      isMirrored.value = mirrorState;
    } catch (e) {
      _logger.e("根据视频类型加载镜像状态失败: $e");
      isMirrored.value = false;
    }
  }

  Future<void> _loadRecordingStatus() async {
    try {
      isRecording.value = await VideoPreferences.loadRecordingStatus();
    } catch (e) {
      _logger.e("加载录制状态时出错: $e");
    }
    update();
  }
}

mixin VideoPlayerMixin on GetxController {
  final Logger _logger = Logger();
  late final VideoInfoState _videoInfoState;
  BetterPlayerController? betterPlayerController;
  RxBool get isPlayerInitialized => _videoInfoState.isPlayerInitialized;
  StreamSubscription? _playerEventSubscription;

  // 添加缺失的属性以修复编译错误
  final RxBool _isInitializing = false.obs;
  BetterPlayerController? _playerController;
  final RxBool _isPlaying = false.obs;

  List<String> getVideoUrlList(dynamic videoDetail) {
    if (!GlobalData.instance.isLoggedIn) {
      _logger.d("检测到本地视频，使用本地视频优先处理逻辑");
      if (videoDetail != null && videoDetail.localVideoPath.isNotEmpty) {
        _logger.i("从videoDetail获取本地视频路径: ${videoDetail.localVideoPath}");
        return [videoDetail.localVideoPath];
      } else if (GlobalData.instance.localVideoPath.isNotEmpty) {
        _logger
            .i("从GlobalData获取本地视频路径: ${GlobalData.instance.localVideoPath}");
        return GlobalData.instance.localVideoPath;
      } else {
        _logger.w("本地视频模式但未找到可用的本地视频路径");
        return [];
      }
    }

    if (videoDetail == null) {
      return [];
    }

    List<String> sources = [
      videoDetail.fullMvHighH264,
      videoDetail.fullMvHighH265,
      videoDetail.fullMvMiddleH264,
      videoDetail.fullMvMiddleH265,
      videoDetail.fullMvSuperH264,
      videoDetail.fullMvSuperH265,
      videoDetail.fullMv4kH264,
      videoDetail.fullMv4kH265,
      videoDetail.fullMvSuper,
    ];

    List<String> availableSources =
        sources.where((s) => s != null && s.isNotEmpty).toList();
    _logger.d("可用视频源数量: ${availableSources.length}");
    return availableSources;
  }

  String getVideoUrl(dynamic videoDetail) {
    if (!GlobalData.instance.isLoggedIn) {
      String? localVideoPath = Get.arguments?['localVideoPath'];
      if (videoDetail != null && videoDetail.localVideoPath != null && videoDetail.localVideoPath.isNotEmpty) {
         _logger.i("从videoDetail获取单个本地视频路径: ${videoDetail.localVideoPath}");
         return 'file://${videoDetail.localVideoPath}';
      } else if (GlobalData.instance.localVideoPath.isNotEmpty) {
        _logger.i("从GlobalData获取单个本地视频路径: ${GlobalData.instance.localVideoPath.first}");
        return GlobalData.instance.localVideoPath.first;
      } else if (localVideoPath != null && localVideoPath.isNotEmpty) {
        _logger.i("从Get.arguments获取本地视频路径: $localVideoPath");
        return 'file://$localVideoPath';
      }
       _logger.w("本地视频模式但未找到可用的本地视频路径");
       return "";
    }
    
    if (videoDetail == null) return "";

    String resolution = GlobalData.instance.currentResolution;
    Map<String, String?> resolutionMap = {
      "1080p": videoDetail.fullMvHighH264 ?? videoDetail.fullMvHighH265,
      "720p": videoDetail.fullMvMiddleH264 ?? videoDetail.fullMvMiddleH265,
      "super": videoDetail.fullMvSuperH264 ?? videoDetail.fullMvSuperH265,
      "4k": videoDetail.fullMv4kH264 ?? videoDetail.fullMv4kH265,
    };

    String? selectedUrl = resolutionMap[resolution];
    List<String> allSources = getVideoUrlList(videoDetail);
    
    if (selectedUrl != null && selectedUrl.isNotEmpty) {
      _logger.d("选择的视频源: $selectedUrl");
      return selectedUrl;
    }
    
    if (allSources.isNotEmpty) {
       _logger.d("选择的视频源: ${allSources.first}");
      return allSources.first;
    }

    _logger.w("未找到可用的视频源");
    return "";
  }

  Future<void> cleanupResources() async {
    _logger.d("清理资源：开始释放相机资源");
    try {
      await PoseCameraController().dispose();
      _logger.d("清理资源：相机资源释放完成");
    } catch (e, stackTrace) {
      _logger.e("清理资源：释放相机资源失败: $e");
    }

    if (betterPlayerController != null) {
      await betterPlayerController!.pause();
      betterPlayerController!.dispose();
      betterPlayerController = null;
    }
    isPlayerInitialized.value = false;
    
    _logger.d("清理全局视频数据缓存");
    GlobalData.instance.localVideoPath = [];
    GlobalData.instance.videoDetail = null;
  }


  Future<void> initializePlayer() async {
    try {
    // 设置初始化状态
    _isInitializing.value = true;
    
    // 获取视频URL列表
    final videoDetail = _videoInfoState.videoDetail.value;
    final videoUrlList = getVideoUrlList(videoDetail);
    
    // 检查是否有可用的视频源
    if (videoUrlList.isEmpty) {
      _logger.w("本地视频模式但未找到可用的视频源，尝试直接使用localVideoPath");
      
      // 获取路由参数
      final arguments = Get.arguments ?? <String, dynamic>{};
      final localVideoPath = arguments['localVideoPath'] as String?;
      final localCoverPath = arguments['localCoverPath'] as String?;
      
      if (localVideoPath != null && localVideoPath.isNotEmpty) {
        _logger.d("尝试使用本地视频路径: $localVideoPath, 封面: $localCoverPath");
        
        // 检查本地视频文件是否存在
        final videoFile = File(localVideoPath);
        final fileExists = await videoFile.exists();
        
        if (fileExists) {
          _logger.i("本地视频文件存在，初始化本地视频播放器");
          
          // 保存本地视频路径到GlobalData
          final globalData = GlobalData.instance;
          globalData.localVideoPaths = [localVideoPath];
          _logger.d("已将本地视频路径保存到GlobalData: [${globalData.localVideoPaths}]");
          
          // 清理资源
          await cleanupResources();
          
          // 处理封面文件
          Widget? placeholderWidget;
          if (localCoverPath != null && localCoverPath.isNotEmpty) {
            final coverFile = File(localCoverPath);
            if (coverFile.existsSync()) {
              placeholderWidget = await BlurredPlaceholderUtil.createBlurredPlaceholder(
                localCoverPath, 
                true
              );
            }
          }
          
          if (placeholderWidget == null) {
            placeholderWidget = Container(color: Colors.black);
          }
          
          // 创建BetterPlayer配置
          final config = BetterPlayerConfiguration(
            aspectRatio: 16 / 9,
            autoPlay: true,
            looping: false,
            fullScreenByDefault: false,
            allowedScreenSleep: false,
            deviceOrientationsAfterFullScreen: [
              DeviceOrientation.portraitUp,
              DeviceOrientation.portraitDown,
            ],
            systemOverlaysAfterFullScreen: [
              SystemUiOverlay.top,
              SystemUiOverlay.bottom,
            ],
            deviceOrientationsOnFullScreen: [
              DeviceOrientation.landscapeLeft,
              DeviceOrientation.landscapeRight,
              DeviceOrientation.portraitUp,
              DeviceOrientation.portraitDown,
            ],
            subtitlesConfiguration: BetterPlayerSubtitlesConfiguration(),
            controlsConfiguration: BetterPlayerControlsConfiguration(),
            fit: BoxFit.contain,
            startAt: Duration.zero,
            handleLifecycle: false,
            autoDetectFullscreenDeviceOrientation: true,
            autoDetectFullscreenAspectRatio: true,
            placeholder: placeholderWidget,
            errorBuilder: (context, errorMessage) => Container(),
          );
          
          // 创建播放器控制器
          _playerController = BetterPlayerController(config);
          
          // 设置数据源
          final dataSource = BetterPlayerDataSource(
            BetterPlayerDataSourceType.file,
            localVideoPath,
            useAsmsSubtitles: false,
            useAsmsTracks: true,
            useAsmsAudioTracks: true,
            cacheConfiguration: BetterPlayerCacheConfiguration(),
            notificationConfiguration: BetterPlayerNotificationConfiguration(),
            bufferingConfiguration: BetterPlayerBufferingConfiguration(),
          );
          
          if (_playerController != null) {
            await _playerController!.setupDataSource(dataSource);
            await _playerController!.play();
          }
          
          // 设置播放状态
          _isPlaying.value = true;
          _logger.i("本地视频播放器初始化成功");
          _isInitializing.value = false;
          return;
        } else {
          _logger.e("本地视频文件不存在: $localVideoPath");
          CommonTips.show(
            "本地视频文件不存在",
            position: TipsPosition.center,
          );
          _isInitializing.value = false;
          return;
        }
      } else {
          _logger.e("本地视频路径为空");
          CommonTips.show(
            "无法播放：本地视频路径为空",
            position: TipsPosition.center,
          );
          _isInitializing.value = false;
          return;
        }
      }
      
      // 如果没有可用的视频源
      _logger.w("没有可用的视频源");
      _isInitializing.value = false;
      return;
      
    } catch (error) {
      _logger.e("初始化播放器时出错: $error");
      _isInitializing.value = false;
      rethrow;
    } finally {
      _isInitializing.value = false;
    }
  }
  
}

mixin VideoPlaybackSpeedMixin on GetxController {
    late final PlaybackSpeedState _speedState;
    late final PlaybackSpeedService _speedService;
    final Logger _logger = Logger();

    Rx<int> get currentPlaybackSpeed => _speedState.currentPlaybackSpeed;
    RxBool get isVipUser => _speedState.isVipUser;

    void initPlaybackSpeed() {
        try {
            _logger.d("初始化倍速播放功能");
            if (!Get.isRegistered<PlaybackSpeedState>()) {
                _speedState = Get.put(PlaybackSpeedState(), permanent: true);
            } else {
                _speedState = Get.find<PlaybackSpeedState>();
            }

            if (!Get.isRegistered<PlaybackSpeedService>()) {
                 _speedService = Get.put(PlaybackSpeedService(), permanent: true);
                 try {
                     _speedService.onInit();
                 } catch (e, s) {
                     _logger.e("PlaybackSpeedService 异步初始化失败: $e");
                 }
            } else {
                _speedService = Get.find<PlaybackSpeedService>();
            }
             _logger.d("倍速播放功能初始化完成");
        } catch (e, stackTrace) {
            _logger.e("倍速播放功能初始化失败: $e");
            _speedService = PlaybackSpeedService(); // Fallback
        }
    }

    List<double> getAvailableSpeedsForUser() {
        return _speedState.getAllAvailableSpeeds();
    }
    
    Future<bool> setPlaybackSpeed(double speed) async {
        try {
            _logger.d("尝试设置倍速: ${speed}x");
            if (speed < 0.25 || speed > 2.0) {
                 _logger.w("倍速值无效: ${speed}x");
                 return false;
            }

            if(PlaybackSpeedUtils.isVipSpeedRequired(speed) && !isVipUser.value){
                _logger.w("倍速需要VIP权限: ${speed}x");
                (this as VideoDetailController)._showVipUpgradeDialog(speed);
                return false;
            }
            
            bool success = await _speedService.setPlaybackSpeed(speed);
            if(success){
                _logger.d("倍速设置成功: ${speed}x");
                await (this as VideoDetailController)._applySpeedToPlayerDirectly(speed);
                (this as VideoDetailController).onSpeedChanged(speed);
            } else {
                _logger.w("倍速设置失败: ${speed}x");
            }
            return success;
        } catch (e) {
            _logger.e("设置倍速异常: $e");
            return false;
        }
    }
}


// --- Main Controller Class ---

class VideoDetailController extends GetxController
    with
        GetTickerProviderStateMixin,
        VideoInteractionMixin,
        VideoSettingsMixin,
        VideoPlayerMixin,
        VideoPlaybackSpeedMixin {
  late final VideoInfoState _videoInfoState;
  final VideoDetailService _videoDetailService = VideoDetailService();
  final VipService _vipService = VipService();
  final ReportService _reportService = ReportService(ApiClient());
  final ShareService _shareService = ShareService();

  late final VideoDetailAnimationController titleAnimationController;
  late AnimationController rankingAnimationController;
  late ChartAnimationUtil chartAnimationUtil;

  // 添加缺失的属性
  final RxBool _isLoadingLocalHistory = false.obs;
  final RxList<LocalVideoScore> _localVideoHistory = <LocalVideoScore>[].obs;
  final RxMap<String, String> _localVideoStatistics = <String, String>{}.obs;
  
  RxString get heroTag => _videoInfoState.heroTag;
  Rx<DanceVideoDetail?> get videoDetail => _videoInfoState.videoDetail;
  Rx<CommunityDetail?> get communityDetail => _videoInfoState.communityDetail;
  RxBool get isLoading => _videoInfoState.isLoading;
  RxInt get currentTabIndex => _videoInfoState.currentTabIndex;
  Rx<String?> get previousRoute => _videoInfoState.previousRoute;
  RxBool get isCommunity => _videoInfoState.isCommunity;
  Rx<DanceScoreDetail?> get danceScoreDetail => _videoInfoState.danceScoreDetail;
  RxList<LocalVideoScore> get scoreHistory => _videoInfoState.scoreHistory;
  RxBool get isLoadingHistory => _videoInfoState.isLoadingHistory;
  RxMap<String, String> get scoreStatistics => _videoInfoState.scoreStatistics;
  String? get localVideoStringId => _videoInfoState.localVideoStringId;
  RxString get videoId => _videoInfoState.videoId;
  Rx<CommunityDetailExtend?> get communityDetailExtend => _videoInfoState.communityDetailExtend;
  Rx<MaterialDetailExtend?> get materialDetailExtend => _videoInfoState.materialDetailExtend;
  RxList<ReportReason> get reportReasons => _reportService.reportReasons;
  RxBool get isLoadingReportReasons => _reportService.isLoadingReportReasons;
  late DateTime shareStartTime;
  bool isShareInProgress = true;

  @override
  Future<void> onInit() async {
    super.onInit();
    
    try {
      var args = Get.arguments ?? {};
      GlobalData.instance.isLocalVideo = args['isLocalVideo'] ?? false;
      _logger.i('视频详情初始化开始: isLocalVideo=${GlobalData.instance.isLocalVideo}');
      _logger.i('路由参数: $args');

      _videoInfoState = Get.put(VideoInfoState());
      initInteractionState();
      initVideoSettings();
      initPlaybackSpeed();
        
      final interactionState = Get.find<InteractionState>();
      _logger.i('交互状态初始化完成：${interactionState.getDebugInfo()}');
      
      rankingAnimationController = AnimationController(vsync: this, duration: const Duration(milliseconds: 500));
      chartAnimationUtil = ChartAnimationUtil();
      titleAnimationController = Get.put(VideoDetailAnimationController());

      if (GlobalData.instance.isDanceVideoPlaying) {
          GlobalData.instance.isDanceVideoPlaying = true;
          await getUserVipStatus();
          
          if (!interactionState.isInitialized.value) {
              String? id = args['id'] ?? args['videoId'];
              if(id != null){
                  var numericId = int.tryParse(id.split('_').first) ?? 0;
                  _videoInfoState.videoId.value = numericId.toString();
                  _videoInfoState.localVideoStringId = id;
                   _logger.i('本地视频使用videoId参数: $id, 数字部分: $numericId');
              } else {
                   _videoInfoState.videoId.value = "0";
                   _videoInfoState.localVideoStringId = "";
                   _logger.w('本地视频ID参数无效');
              }

              _videoInfoState.previousRoute.value = args['from'] ?? '/main';
              _videoInfoState.isCommunity.value = args['isCommunity'] ?? false;
              interactionState.init(
                _videoInfoState.videoId.value,
                _videoInfoState.isCommunity.value
              );

              String videoTitle = args['videoTitle'] ?? '本地视频';
              String coverPath = args['localCoverPath'] ?? '';
              String videoPath = args['localVideoPath'] ?? '';
              String bodyDataPath = args['localBodyDataPath'] ?? '';
              
              _videoInfoState.videoDetail.value = DanceVideoDetail(
                name: videoTitle,
                coverUrl: coverPath,
                localVideoPath: videoPath,
                localBodyDataPath: bodyDataPath,
                // other fields...
              );
               _logger.i('本地视频信息已更新: 视频路径=$videoPath, 封面路径=$coverPath');

               await loadVideoPlaySettings();
               await _loadLocalVideoMirrorState();
               _logger.i("开始为本地视频加载得分详情");
               await loadDanceScoreDetail();
               _logger.i("开始为本地视频加载历史记录");
               await loadLocalVideoHistory();
          }

          update(['video_content', 'video_content_inner']);
          _notifyUpdate();
      } else {
          await _initData();
      }

      await registerWxApi(
          appId: "wx_app_id_placeholder", // Replace with actual App ID
          universalLink: "your_universal_link" // Replace with actual Universal Link
      );

      shareStartTime = DateTime.now();
      isShareInProgress = true;
      responseEventHandler.listen((response) {
          if (response is WeChatShareResponse) {
             if(response.errCode == 0){
                Duration diff = DateTime.now().difference(shareStartTime);
                if(diff.inSeconds > 5 && isShareInProgress){
                   addShareCount();
                   isShareInProgress = false;
                }
             }
          }
      });
      
    } catch (e, stackTrace) {
      _logger.e('VideoDetailController初始化错误: $e');
    }
  }

  @override
  Future<void> onClose() async {
      _logger.i('开始释放相机资源 - onClose');
      try {
          await PoseCameraController().dispose();
          _logger.i('相机资源释放完成');
      } catch(e) {
          _logger.e('释放相机资源失败: $e');
      }
      
      _playerEventSubscription?.cancel();
      _playerEventSubscription = null;

      GlobalData.instance.isDanceVideoPlaying = false;
      await cleanupResources();
      
      _logger.i('清理全局视频数据 - onClose');
      GlobalData.instance.localVideoPath = <String>[];
      GlobalData.instance.videoDetail = null;
      
      resetVideoState();
      
      if(Get.isRegistered<CommunityPanelController>()){
          Get.find<CommunityPanelController>().clearAll();
      }

      titleAnimationController.dispose();
      rankingAnimationController.dispose();
      chartAnimationUtil.dispose();
      
      super.onClose();
  }
  
  Future<void> _initData() async {
    try {
        _videoInfoState.isLoading.value = true;
        
        if (GlobalData.instance.isLocalVideo) {
            _logger.i('_initData - 检测到本地视频，跳过所有网络请求');
            _videoInfoState.isLoading.value = false;
            return;
        }

        _logger.i('_initData - 开始加载网络数据，videoId=${_videoInfoState.videoId.value}');

        if (_videoInfoState.isCommunity.value) {
            await loadCommunityVideoDetails();
            await loadCommunityDetailExtend();
        } else {
            await loadVideoDetails();
            await loadMaterialDetailExtend();
        }
        
        if (!GlobalData.instance.isLocalVideo) {
            if (_videoInfoState.isCommunity.value) {
                await loadDanceScoreDetail();
            }
            await loadMaxScoreSortList();
            await loadInteractionData();
        }
    } catch (e, stackTrace) {
        _logger.e('初始化数据失败: $e');
        _videoInfoState.isLoading.value = false;
    } finally {
        await initializePlayer();
        _videoInfoState.isLoading.value = false;
    }
  }

  /// 添加分享计数
  /// 
  /// 从materialDetailExtend中获取materialId，然后调用ShareService的addShareCount方法
  Future<void> addShareCount() async {
    try {
      // 获取materialDetailExtend中的materialId
      final materialDetailExtend = _videoInfoState.materialDetailExtend.value;
      if (materialDetailExtend == null) {
        _logger.w('materialDetailExtend为空，无法获取materialId');
        return;
      }
      
      final int materialId = materialDetailExtend.materialId;
      
      // 调用ShareService的addShareCount方法
      final bool success = await _shareService.addShareCount(materialId);
      
      if (!success) {
        _logger.e('添加分享计数失败');
      }
    } catch (e, stackTrace) {
      _logger.e('添加分享计数失败: $e');
    }
  }

  Future<void> _initOtherData() async {
    try {
       _logger.i('_initOtherData - 开始加载其他网络数据，videoId=${_videoInfoState.videoId.value}');

       if (GlobalData.instance.isLocalVideo) {
         _logger.i("跳过为本地视频加载网络数据");
         return;
       }

       if (_videoInfoState.isCommunity.value) {
          if (!GlobalData.instance.isLocalVideo) {
            await loadDanceScoreDetail();
          }
       } else {
          await loadMaxScoreSortList();
          await loadInteractionData();
       }
       update();
    } catch(e, stackTrace) {
       _logger.e("初始化其他数据时出错: $e");
    }
  }

  Future<void> loadVideoDetails() async {
     try {
       var detail = await _videoDetailService.loadVideoDetails(_videoInfoState.videoId.value);
       if (detail != null) {
          _videoInfoState.videoDetail.value = detail;
          GlobalData.instance.videoDetail = detail;
          await titleAnimationController.updateStatusBarColor(detail.cover ?? '');
          titleAnimationController.startTitleAnimation();
       }
     } catch (e, stackTrace) {
       _logger.e("处理视频详情时出错: $e");
     } finally {
        update();
     }
  }
  
  Future<void> loadCommunityVideoDetails() async {
    try {
      var result = await _videoDetailService.loadCommunityVideoDetails(_videoInfoState.videoId.value);
      if (result != null) {
        final (communityDetail, danceVideoDetail) = result;
        if (communityDetail != null) {
          _videoInfoState.communityDetail.value = communityDetail;
        }
        if (danceVideoDetail != null) {
          _videoInfoState.videoDetail.value = danceVideoDetail;
          GlobalData.instance.videoDetail = _videoInfoState.videoDetail.value;
        }
      }
    } catch (e, stackTrace) {
        _logger.e("处理社区视频详情时出错: $e");
        _videoInfoState.isLoading.value = false;
    }
  }
  
  Future<void> loadLocalVideoScoreDetail() async {
    try {
      final scoreService = Get.find<LocalVideoScoreService>();
      await scoreService.ensureDatabaseInitialized();
      var bestScore = await scoreService.getBestLocalVideoScore(_videoInfoState.localVideoStringId!);
      if (bestScore != null) {
        _videoInfoState.danceScoreDetail.value = DanceScoreDetail.fromJson(bestScore.toDanceScoreDetailJson());
      } else {
        _videoInfoState.danceScoreDetail.value = null;
      }
      update();
    } catch (e, stackTrace) {
      _logger.e("加载本地视频得分详情失败: $e");
       _videoInfoState.danceScoreDetail.value = null;
       update();
    }
  }
  
  Future<void> loadDanceScoreDetail() async {
    try {
        if (GlobalData.instance.isLocalVideo) {
          _logger.i("跳过为本地视频加载进度指标");
          return;
        }
        var detail = await _videoDetailService.loadDanceScoreDetail(_videoInfoState.videoId.value);
        if (detail != null) {
           _videoInfoState.organSort.value = detail.organSort ?? "0";
           _videoInfoState.progressRank.value = "0";
           _videoInfoState.progressRate.value = "0.0";
        }
        update();
    } catch (e, stackTrace) {
        _logger.e("加载用户舞蹈进度指标数据时出错: $e");
    }
  }
  
  Future<void> loadMaxScoreSortList() async {
    try {
      // 检查是否为本地视频，如果是则跳过网络请求
      if (GlobalData.instance.isLocalVideo) {
        _logger.i('本地视频模式，跳过加载排行榜数据');
        return;
      }

      // 获取视频ID
      int videoId = int.tryParse(_videoInfoState.videoId.value) ?? 0;
      
      // 调用服务加载排行榜数据
      var rankingData = await _videoDetailService.loadMaxScoreSortList(videoId);
      
      // 如果数据不为空，更新状态并启动动画
      if (rankingData != null) {
        _videoInfoState.maxScoreSortList.value = rankingData.userSortList;
        startRankingAnimation();
      }
      
      // 通知UI更新
      update();
    } catch (e, stackTrace) {
      _logger.e("加载排行榜数据时出错: $e");
    }
  }
  
  Future<void> loadLocalVideoHistory() async {
    try {
      // 设置加载状态为true
      _isLoadingLocalHistory.value = true;
      
      // 获取LocalVideoScoreService实例
      final localVideoScoreService = Get.find<LocalVideoScoreService>();
      
      // 确保数据库已初始化
      await localVideoScoreService.ensureDatabaseInitialized();
      
      // 检查_videoInfoState是否已初始化
      if (_videoInfoState == null) {
        throw Exception('_videoInfoState not initialized');
      }
      
      // 获取本地视频历史记录
      final historyList = await localVideoScoreService.getLocalVideoScoreHistory(_videoInfoState.localVideoStringId ?? '');
      
      // 获取本地视频统计信息
      final statistics = await localVideoScoreService.getLocalVideoScoreStatistics();
      
      // 更新历史记录列表
      _localVideoHistory.value = historyList;
      
      // 更新统计信息
      _localVideoStatistics.value = statistics.cast<String, String>();
      
      // 记录成功日志
      _logger.i("本地视频历史记录加载完成: ${historyList.length} 条记录");
      
      // 更新UI
      update();
      
    } catch (e, stackTrace) {
      // 记录错误日志
      _logger.e("加载本地视频历史记录失败: $e");
      
      // 清空数据
      _localVideoHistory.clear();
      _localVideoStatistics.clear();
      
    } finally {
      // 设置加载状态为false
      _isLoadingLocalHistory.value = false;
    }
  }
  
  Future<void> loadMaterialDetailExtend() async {
    try {
      // 检查用户是否已登录
      if (!GlobalData.instance.isLoggedIn) {
        return;
      }

      // 获取视频ID
      String videoId = _videoInfoState.videoId.value;
      
      // 调用服务加载素材扩展详情
      var materialDetailExtendData = await _videoDetailService.loadMaterialDetailExtend(videoId);
      
      // 如果数据不为空，更新状态
      if (materialDetailExtendData != null) {
        _videoInfoState.materialDetailExtend.value = materialDetailExtendData;
        _logger.i('素材扩展详情加载成功');
      }
      
      // 通知UI更新
      update();
    } catch (error, stackTrace) {
      _logger.e('处理素材扩展详情时出错: $error');
    }
  }
  
  loadCommunityDetailExtend() async {
    try {
      // 检查_videoInfoState是否已初始化
      if (_videoInfoState == null) {
        throw Exception('_videoInfoState not initialized');
      }
      
      // 获取视频ID
      int videoId = int.tryParse(_videoInfoState.videoId.value) ?? 0;
      
      // 调用服务加载社区视频扩展详情
      var communityDetailExtendData = await _videoDetailService.loadCommunityDetailExtend(videoId);
      
      // 如果数据不为空，更新状态
      if (communityDetailExtendData != null) {
        _videoInfoState.communityDetailExtend.value = communityDetailExtendData;
        _logger.i('社区视频扩展详情加载成功');
      }
      
      // 通知UI更新
      update();
    } catch (error, stackTrace) {
      _logger.e('处理社区视频扩展详情时出错: $error');
    }
  }
  
  Future<void> getUserVipStatus() async {
    try {
      var status = await _vipService.getUserVipStatus();
      _videoInfoState.vipStatus.value = status ? 1 : 0;
      update();
    } catch (e, stackTrace) {
      _logger.e("获取会员状态失败: $e");
    }
  }
  
  Future<bool> checkTodayLimit(int videoId) async {
      update();
      return await _vipService.checkTodayLimit(videoId);
  }


  void resetVideoState() {
    try {
      // 记录重置状态日志
      _logger.i("重置视频详情状态");
      
      // 保存和恢复本地视频得分数据
      // 保存本地视频得分数据
      var localVideoScoreData = _videoInfoState.danceScoreDetail.value;
      _logger.i("保存本地视频得分数据: $localVideoScoreData");
      
      // 清理视频信息状态
      _videoInfoState.clear();
      
      // 恢复本地视频得分数据
      _videoInfoState.danceScoreDetail.value = localVideoScoreData;
      _logger.i("恢复本地视频得分数据: $localVideoScoreData");
      
      // 重置镜像状态
      try {
        if (Get.isRegistered<PlayerSettingsState>()) {
          var playerSettings = Get.find<PlayerSettingsState>();
          playerSettings.isMirror.value = false;
          _logger.i("🔧 已重置镜像状态，防止状态污染");
        }
      } catch (e) {
        _logger.e("重置镜像状态时出错: $e");
      }
      
      // 重置交互状态
      try {
        if (Get.isRegistered<InteractionState>()) {
          var interactionState = Get.find<InteractionState>();
          // 直接重置状态，不使用safeReset方法
          interactionState.isLiked.value = false;
          interactionState.isCollected.value = false;
          interactionState.isFollowing.value = false;
          _logger.i("成功安全重置InteractionState");
        } else {
          _logger.w("InteractionState实例未注册，跳过重置");
        }
      } catch (e) {
        _logger.e("重置InteractionState时出错: $e");
      }
      
      // 清理全局数据
      // GlobalData.instance.localVideoPath = [].obs;
      // GlobalData.instance.videoDetail = null;
      
      // 检查当前路由并设置相应状态
      try {
        String currentRoute = Get.currentRoute;
        if (currentRoute == "/video-detail") {
          // GlobalData.instance.isDanceVideoPlaying = false;
        }
      } catch (e) {
        _logger.e("重置视频状态时出错: $e");
      }
      
    } catch (e) {
      _logger.e("重置视频状态时出错: $e");
    }
  }

  
  Future<void> getReportReasonList() async {
      if (_reportService.reportReasons.isNotEmpty) {
          return;
      }
      _reportService.isLoadingReportReasons.value = true;
      try {
          var reasons = await _reportService.getReportReasonList();
          if (reasons != null) {
              _reportService.reportReasons.value = reasons;
          }
      } catch (e, stackTrace) {
          _logger.e("获取举报原因列表出错: $e");
      } finally {
           _reportService.isLoadingReportReasons.value = false;
           update();
      }
  }

  Future<(bool, String)> submitReport(int reasonId, String content, String contact, List<String> images, String type, int targetId) async {
    return await _reportService.submitReport(
      materialId: targetId,
      reasonKeyList: [reasonId.toString()],
      twoReasonKeyList: [],
      desc: content,
      reportImgList: images,
      phone: contact,
    );
  }
  
  void startRankingAnimation() {
    rankingAnimationController.reset();
    rankingAnimationController.forward();
  }
  
  void resetChartAnimation() {
      if(Get.isRegistered<ChartAnimationUtil>()){
          Get.find<ChartAnimationUtil>().resetAnimation();
      }
  }
  
  void startChartAnimation() {
      if(Get.isRegistered<ChartAnimationUtil>()){
        var util = Get.find<ChartAnimationUtil>();
        if(util.animationController == null) {
            util.initialize(this, () {
                update(['analysis_tab']);
            });
        }
        util.startAnimation(isDanceAnalysisTab: _videoInfoState.currentTabIndex.value == 2);
      }
  }
  
  void onTabChanged(int index) {
      if (_videoInfoState.currentTabIndex.value == 2 && index != 2) {
          resetChartAnimation();
      }
      _videoInfoState.currentTabIndex.value = index;
      titleAnimationController.onTabChanged(index);

      if (index == 0) {
          update(['score_tab']);
      } else if (index == 1) {
          if (!GlobalData.instance.isLocalVideo && scoreHistory.isEmpty) {
              loadLocalVideoHistory();
          }
          update(['local_history_content', 'local_history_list']);
      } else if (index == 2) {
         startChartAnimation();
         update(['analysis_tab']);
      } else {
         update();
      }
  }

  Map<String, String> getCurrentVideoStats() {
    final history = _videoInfoState.scoreHistory.value;
    if(history.isEmpty){
        return {
            "totalCount": "0",
            "bestScore": "0",
            "averageScore": "0",
        };
    }
    double totalScore = history.map((e) => double.tryParse(e.maxScore) ?? 0.0).reduce((a, b) => a + b);
    double bestScore = history.map((e) => double.tryParse(e.maxScore) ?? 0.0).reduce((a, b) => a > b ? a : b);
    int validCount = history.where((e) => (double.tryParse(e.maxScore) ?? 0.0) > 0).length;
    double averageScore = validCount > 0 ? totalScore / validCount : 0.0;
    
    _logger.d("平均分计算优化: 总记录${history.length}条, 有效记录${validCount}条, 平均分${averageScore.toStringAsFixed(1)}");
    
    return {
        "totalCount": history.length.toString(),
        "bestScore": bestScore.toStringAsFixed(1),
        "averageScore": averageScore.toStringAsFixed(1),
    };
  }

  void trackVideoModeUsage(String mode) {
     // Implementation for analytics tracking
  }

  void trackButtonClick(String buttonName, String action, {
    Function? trackCallback,
    String? resolutionValue,
    String? sharePlatform,
    String? settingsType,
    String? privacyMode,
  }) {
    _trackButtonClick(buttonName, action,
      trackCallback: trackCallback,
      resolutionValue: resolutionValue,
      sharePlatform: sharePlatform,
      settingsType: settingsType,
      privacyMode: privacyMode,
    );
  }

  void _trackButtonClick(String buttonName, String action, {
    Function? trackCallback,
    String? resolutionValue,
    String? sharePlatform,
    String? settingsType,
    String? privacyMode,
  }) {
    if (trackCallback != null) {
      trackCallback(buttonName, action);
      return;
    }

    var videoDetail = _videoInfoState.videoDetail.value;
    if (videoDetail == null) return;
    
    var homeController = Get.find<HomeController>();

    EventAnalytics.trackVideoButtonClick(
      action,
      buttonName,
      homeController.tabIndex.value.toString(),
      _videoInfoState.isCommunity.value,
      resolutionValue,
      null, // sharePlatform
      videoDetail.netId ?? '',
      videoDetail.title ?? '',
    );
  }

  Future<void> _initializeScreenOrientation() async {
    try {
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
      update();
    } catch(e) {
      _logger.e('Error setting screen orientation: $e');
    }
  }

  // 添加缺失的方法以修复编译错误
  Future<void> _loadLocalVideoMirrorState() async {
    try {
      // 加载本地视频镜像状态的逻辑
      _logger.i('加载本地视频镜像状态');
      // 这里可以添加具体的镜像状态加载逻辑
    } catch (e) {
      _logger.e('加载本地视频镜像状态失败: $e');
    }
  }

  void _notifyUpdate() {
    try {
      // 通知更新的逻辑
      _logger.i('通知更新');
      update();
    } catch (e) {
      _logger.e('通知更新失败: $e');
    }
  }

  /// 显示VIP升级对话框
  Future<void> _showVipUpgradeDialog(double speed) async {
    try {
      _logger.i('显示VIP升级对话框，倍速: ${speed}x');
      // 这里可以添加显示VIP升级对话框的逻辑
      // 例如：Get.dialog(VipUpgradeDialog(speed: speed));
    } catch (e) {
      _logger.e('显示VIP升级对话框失败: $e');
    }
  }

  /// 直接应用倍速到播放器
  Future<void> _applySpeedToPlayerDirectly(double speed) async {
    try {
      _logger.i('直接应用倍速到播放器: ${speed}x');
      if (betterPlayerController != null) {
        await betterPlayerController!.setSpeed(speed);
      }
    } catch (e) {
      _logger.e('直接应用倍速到播放器失败: $e');
    }
  }

  /// 倍速改变回调
  void onSpeedChanged(double speed) {
    try {
      _logger.i('倍速改变回调: ${speed}x');
      // 这里可以添加倍速改变后的处理逻辑
      update();
    } catch (e) {
      _logger.e('倍速改变回调失败: $e');
    }
  }

}