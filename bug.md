PS D:\project\ai-dance\legend_dance> flutter run -d FMR0224924017063
Flutter assets will be downloaded from https://storage.flutter-io.cn. Make sure you trust this source!
Launching lib\main.dart on ALN AL00 in debug mode...
Flutter assets will be downloaded from https://storage.flutter-io.cn. Make sure you trust this source!
lib/pages/creation/services/dance_share_service.dart:157:38: Error: Undefined name 'isWeChatInstalled'.
      bool isInstalled = await fluwx.isWeChatInstalled;
                                     ^^^^^^^^^^^^^^^^^
lib/pages/creation/services/dance_share_service.dart:210:26: Error: Undefined name 'responseEventHandler'.
      subscriber = fluwx.responseEventHandler.listen((fluwx.WeChatResponse response) {
                         ^^^^^^^^^^^^^^^^^^^^
lib/pages/creation/services/dance_share_service.dart:220:19: Error: Method not found: 'share'.
      await fluwx.share(model);
                  ^^^^^
lib/pages/creation/services/dance_share_service.dart:201:14: Error: The argument type 'String' can't be assigned to the parameter type 'WeChatFile'.
 - 'WeChatFile' is from 'package:fluwx/src/wechat_file.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/fluwx-5.7.2/lib/src/wechat_file.dart').
        file.path,
             ^
lib/pages/creation/services/dance_share_service.dart:382:19: Error: Method not found: 'registerApi'.
      await fluwx.registerApi(
                  ^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:914:13: Error: The method 'registerWxApi' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing method, or defining a method named 'registerWxApi'.
      await registerWxApi(
            ^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:921:7: Error: The getter 'responseEventHandler' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'responseEventHandler'.
      responseEventHandler.listen((response) {
      ^^^^^^^^^^^^^^^^^^^^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/drm_init_data.dart:22:23: Error: The method 'hashValues' isn't defined for the class 'DrmInitData'.
 - 'DrmInitData' is from 'package:better_player/src/hls/hls_parser/drm_init_data.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/drm_init_data.dart').
Try correcting the name to the name of an existing method, or defining a method named 'hashValues'.
  int get hashCode => hashValues(schemeType, schemeData);
                      ^^^^^^^^^^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/scheme_data.dart:52:23: Error: The method 'hashValues' isn't defined for the class 'SchemeData'.
 - 'SchemeData' is from 'package:better_player/src/hls/hls_parser/scheme_data.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/scheme_data.dart').
Try correcting the name to the name of an existing method, or defining a method named 'hashValues'.
  int get hashCode => hashValues(
                      ^^^^^^^^^^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/hls_track_metadata_entry.dart:31:23: Error: The method 'hashValues' isn't defined for the class 'HlsTrackMetadataEntry'.
 - 'HlsTrackMetadataEntry' is from 'package:better_player/src/hls/hls_parser/hls_track_metadata_entry.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/hls_track_metadata_entry.dart').
Try correcting the name to the name of an existing method, or defining a method named 'hashValues'.
  int get hashCode => hashValues(groupId, name, variantInfos);
                      ^^^^^^^^^^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/variant_info.dart:44:23: Error: The method 'hashValues' isn't defined for the class 'VariantInfo'.
 - 'VariantInfo' is from 'package:better_player/src/hls/hls_parser/variant_info.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/variant_info.dart').
Try correcting the name to the name of an existing method, or defining a method named 'hashValues'.
  int get hashCode => hashValues(
                      ^^^^^^^^^^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/palette_generator-0.3.3/lib/palette_generator.dart:637:12: Error: The method 'hashValues' isn't defined for the class 'PaletteTarget'.
 - 'PaletteTarget' is from 'package:palette_generator/palette_generator.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/palette_generator-0.3.3/lib/palette_generator.dart').
Try correcting the name to the name of an existing method, or defining a method named 'hashValues'.
    return hashValues(
           ^^^^^^^^^^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/palette_generator-0.3.3/lib/palette_generator.dart:856:12: Error: The method 'hashValues' isn't defined for the class 'PaletteColor'.
 - 'PaletteColor' is from 'package:palette_generator/palette_generator.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/palette_generator-0.3.3/lib/palette_generator.dart').
Try correcting the name to the name of an existing method, or defining a method named 'hashValues'.
    return hashValues(color, population);
           ^^^^^^^^^^
Target kernel_snapshot_program failed: Exception


FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':app:compileFlutterBuildDebug'.
> Process 'command 'D:\Program Files\Flutterdev\flutter\bin\flutter.bat'' finished with non-zero exit value 1

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

BUILD FAILED in 5s
Running Gradle task 'assembleDebug'...                              6.1s
Error: Gradle task assembleDebug failed with exit code 1